/*
 * Copyright (C) 2024 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

package com.android.server.ai.production;

import android.os.Bundle;
import android.os.Parcel;
import android.os.Parcelable;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

/**
 * Result of a deployment operation
 */
public class DeploymentResult implements Parcelable {
    private String mDeploymentId;
    private boolean mSuccess;
    private String mMessage;
    private String mErrorMessage;
    private long mStartTime;
    private long mEndTime;
    private Map<String, PhaseResult> mPhaseResults;
    private Bundle mMetrics;
    private SecurityHardeningResult mSecurityResult;
    private QualityAssuranceResult mQualityAssuranceResult;
    private DocumentationGenerationResult mDocumentationResult;
    
    public DeploymentResult() {
        mPhaseResults = new HashMap<>();
        mMetrics = new Bundle();
    }
    
    public DeploymentResult(String deploymentId) {
        this();
        mDeploymentId = deploymentId;
        mStartTime = System.currentTimeMillis();
    }
    
    public DeploymentResult(Parcel in) {
        mDeploymentId = in.readString();
        mSuccess = in.readBoolean();
        mMessage = in.readString();
        mErrorMessage = in.readString();
        mStartTime = in.readLong();
        mEndTime = in.readLong();
        mPhaseResults = new HashMap<>();
        mMetrics = in.readBundle(getClass().getClassLoader());
    }
    
    @Override
    public void writeToParcel(Parcel dest, int flags) {
        dest.writeString(mDeploymentId);
        dest.writeBoolean(mSuccess);
        dest.writeString(mMessage);
        dest.writeString(mErrorMessage);
        dest.writeLong(mStartTime);
        dest.writeLong(mEndTime);
        dest.writeBundle(mMetrics);
    }
    
    @Override
    public int describeContents() {
        return 0;
    }
    
    public static final Creator<DeploymentResult> CREATOR = new Creator<DeploymentResult>() {
        @Override
        public DeploymentResult createFromParcel(Parcel in) {
            return new DeploymentResult(in);
        }
        
        @Override
        public DeploymentResult[] newArray(int size) {
            return new DeploymentResult[size];
        }
    };
    
    // Getters and setters
    public String getDeploymentId() { return mDeploymentId; }
    public void setDeploymentId(String deploymentId) { mDeploymentId = deploymentId; }
    
    public boolean isSuccess() { return mSuccess; }
    public void setSuccess(boolean success) { mSuccess = success; }
    
    public String getMessage() { return mMessage; }
    public void setMessage(String message) { mMessage = message; }
    
    public String getErrorMessage() { return mErrorMessage; }
    public void setErrorMessage(String errorMessage) { mErrorMessage = errorMessage; }
    
    public long getStartTime() { return mStartTime; }
    public void setStartTime(long startTime) { mStartTime = startTime; }
    
    public long getEndTime() { return mEndTime; }
    public void setEndTime(long endTime) { mEndTime = endTime; }
    
    public long getDuration() { return mEndTime - mStartTime; }
    
    public Bundle getMetrics() { return mMetrics; }
    public void setMetrics(Bundle metrics) { mMetrics = metrics; }
    
    public void addPhaseResult(String phase, boolean success, String message) {
        mPhaseResults.put(phase, new PhaseResult(phase, success, message));
    }
    
    public Map<String, PhaseResult> getPhaseResults() { return mPhaseResults; }
    
    public void setSecurityHardeningResult(SecurityHardeningResult result) { mSecurityResult = result; }
    public SecurityHardeningResult getSecurityHardeningResult() { return mSecurityResult; }
    
    public void setQualityAssuranceResult(QualityAssuranceResult result) { mQualityAssuranceResult = result; }
    public QualityAssuranceResult getQualityAssuranceResult() { return mQualityAssuranceResult; }
    
    public void setDocumentationResult(DocumentationGenerationResult result) { mDocumentationResult = result; }
    public DocumentationGenerationResult getDocumentationResult() { return mDocumentationResult; }
    
    public static class PhaseResult {
        public final String phase;
        public final boolean success;
        public final String message;
        public final long timestamp;
        
        public PhaseResult(String phase, boolean success, String message) {
            this.phase = phase;
            this.success = success;
            this.message = message;
            this.timestamp = System.currentTimeMillis();
        }
    }
}
